// frontend/src/app/layout.tsx
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import "@copilotkit/react-ui/styles.css"; // Add CopilotKit styles
import { SupabaseProvider } from "@/lib/supabase/provider";
import { SecurityProvider } from "@/lib/security";
import type { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { FingerprintJSClientProvider } from "@/components/providers/fpjs-provider";
import { SessionProvider } from "@/contexts/SessionContext";
import { UserProvider } from "@/contexts/UserContext";
import { MonitoringInitializer } from "@/lib/monitoring/monitoring-initializer";
import ConnectionStatusBannerWrapper from "@/components/ui/connection-status-banner-wrapper";
import { ConfigInitializer } from "@/components/providers/config-initializer";
import { getCopilotKitConfig } from "@/lib/env/client";

// Force dynamic rendering for the entire app since we use SessionProvider
export const dynamic = "force-dynamic";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pi Lawyer AI",
  description: "Personal Injury Lawyer AI Assistant",
};

export default function RootLayout({
  children,
}: {
  children: ReactNode;
}): JSX.Element {
  const copilotConfig = getCopilotKitConfig();

  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={`${inter.className} min-h-screen bg-background antialiased`}
      >
        <FingerprintJSClientProvider>
          <SessionProvider>
            {/* Configuration validation on startup */}
            <ConfigInitializer>
              {/* Monitoring system for error reporting - moved inside SessionProvider */}
              <MonitoringInitializer>
                {/* Show global connection status banner inside SessionProvider */}
                <ConnectionStatusBannerWrapper />
                <UserProvider>
                  <SupabaseProvider>
                    <SecurityProvider>
                      <CopilotKit
                        runtimeUrl="/api/copilotkit/langgraph"
                        {...({ baseUrl: "/api/copilotkit/langgraph" } as any)}
                      >
                        <Toaster richColors closeButton />
                        {children}
                      </CopilotKit>
                    </SecurityProvider>
                  </SupabaseProvider>
                </UserProvider>
              </MonitoringInitializer>
            </ConfigInitializer>
          </SessionProvider>
        </FingerprintJSClientProvider>
      </body>
    </html>
  );
}
