"""
Lightweight Intake Agent handler for CopilotKit integration.

This avoids heavy imports at startup and lazily loads the IntakeAgent
from the pi_lawyer package only when requests arrive.
"""

from __future__ import annotations

import logging
import uuid
from typing import Any, Dict

logger = logging.getLogger(__name__)


class IntakeCopilotHandler:
  """Handle CopilotKit requests for the Intake Agent with lazy imports."""

  def __init__(self) -> None:
    self._agent = None
    self._initialized = False

  def _ensure_initialized(self) -> None:
    if self._initialized:
      return
    try:
      # Import IntakeAgent from installed pi_lawyer package (src/)
      from pi_lawyer.agents.interactive.intake.agent import IntakeAgent  # type: ignore

      self._agent = IntakeAgent()
      self._initialized = True
      logger.info("Intake agent initialized successfully (lite handler)")
    except Exception as e:
      logger.exception("Failed to initialize IntakeAgent: %s", e)
      self._agent = None
      self._initialized = True

  async def handle_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
    """Process an Intake Agent request and return CopilotKit-compatible response."""
    self._ensure_initialized()

    thread_id = data.get("threadId") or str(uuid.uuid4())
    user_id = data.get("userId") or "anonymous"
    tenant_id = data.get("tenantId") or "default"

    # Extract the last user message text
    user_text = ""
    msgs = data.get("messages") or []
    for m in reversed(msgs):
      if (m.get("role") or "").lower() == "user":
        content = m.get("content")
        if isinstance(content, list) and content and isinstance(content[0], str):
          user_text = content[0]
        elif isinstance(content, str):
          user_text = content
        break

    if not user_text:
      user_text = "Hello"

    # If agent failed to initialize, return a graceful response
    if not self._agent:
      return {
        "messages": [
          {
            "role": "assistant",
            "content": [
              "Intake agent is temporarily unavailable. Please try again later."
            ],
          }
        ],
        "done": True,
        "threadId": thread_id,
      }

    try:
      # Build state in the minimal format IntakeAgent expects
      state = {
        "messages": [
          {"type": "human", "content": user_text},
        ]
      }
      # Staff mode by default for admin pages
      if data.get("context") == "intake" or data.get("agent") == "intake_agent":
        state["intake_mode"] = "staff"

      config = {
        "configurable": {
          "thread_id": thread_id,
          "tenant_id": tenant_id,
          "user_id": user_id,
        }
      }

      # Execute the agent lifecycle without full LangGraph persistence
      # __call__ runs initialize -> execute -> cleanup and avoids external DB
      result_state = await self._agent(state, config)  # type: ignore[misc]

      # Extract last assistant/ai message
      assistant_text = ""
      rmsgs = result_state.get("messages") if isinstance(result_state, dict) else []
      if isinstance(rmsgs, list):
        for m in reversed(rmsgs):
          role = (m.get("role") or m.get("type") or "").lower() if isinstance(m, dict) else ""
          content = m.get("content") if isinstance(m, dict) else None
          if role in ("assistant", "ai") and isinstance(content, str) and content.strip():
            assistant_text = content.strip()
            break

      if not assistant_text:
        assistant_text = "I collected your intake details. What else can I help with?"

      return {
        "messages": [{"role": "assistant", "content": [assistant_text]}],
        "done": True,
        "threadId": thread_id,
      }

    except Exception as e:
      logger.exception("Intake agent invocation failed: %s", e)
      return {
        "messages": [
          {
            "role": "assistant",
            "content": [
              "I ran into an error running the intake flow. Please try again."
            ],
          }
        ],
        "done": True,
        "threadId": thread_id,
      }
