"""
CopilotKit integration route for FastAPI.

This module provides endpoints for CopilotKit to interact with our LangGraph agents.
"""

import logging
from typing import Any, Dict, Optional

from fastapi import APIRouter, HTTPException, Request, Header, Depends

# Use lightweight handlers to avoid heavy imports during startup
from backend.agents.interactive.research.copilot_handler_lite import (
    ResearchCopilotHandler,
)
from backend.agents.interactive.intake.copilot_handler_lite import (
    IntakeCopilotHandler,
)

# TODO: Create lightweight deadline handler
# from backend.agents.interactive.deadline.copilot_handler import DeadlineCopilotHandler

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/copilotkit",
    tags=["copilotkit"],
    responses={404: {"description": "Not found"}},
)

# Initialize handlers with lightweight implementations
research_handler = ResearchCopilotHandler()
# TODO: Create deadline handler
# deadline_handler = DeadlineCopilotHandler()
intake_handler = IntakeCopilotHandler()

# Agent mapping
AGENT_HANDLERS = {
    "research_agent": research_handler,
    "intake_agent": intake_handler,
    # "deadline_agent": deadline_handler,  # TODO: Add when deadline handler is ready
}


# -----------------------------
# Auth: Endpoint key validation
# -----------------------------
def require_endpoint_secret(
    x_cpk_endpoint_key: Optional[str] = Header(default=None, alias="X-CPK-Endpoint-Key"),
):
    """Require the correct endpoint key header for CopilotKit calls.

    - Returns 500 if backend not configured with CPK_ENDPOINT_SECRET
    - Returns 401 if provided header is missing/invalid
    """
    import os
    secret = os.getenv("CPK_ENDPOINT_SECRET", "")
    if not secret:
        logger.error("CPK_ENDPOINT_SECRET not configured on backend")
        raise HTTPException(status_code=500, detail="Endpoint secret not configured")
    if x_cpk_endpoint_key != secret:
        logger.warning("Invalid X-CPK-Endpoint-Key")
        raise HTTPException(status_code=401, detail="Invalid endpoint key")


# -----------------
# Health endpoint
# -----------------
@router.get("/health")
async def copilotkit_health():
    """Report readiness of AI providers used by CopilotKit handlers.

    Returns 200 with ok=false on internal errors to avoid 500s for health probes.
    """
    try:
        import os
        openai_ready = bool(os.getenv("OPENAI_API_KEY"))
        anthropic_ready = bool(os.getenv("ANTHROPIC_API_KEY"))
        return {
            "ok": bool(openai_ready or anthropic_ready),
            "providers": {
                "openai": openai_ready,
                "anthropic": anthropic_ready,
            },
        }
    except Exception as e:
        logger.exception("CopilotKit health error: %s", e)
        return {"ok": False, "error": str(e)}


@router.post("")
async def handle_copilotkit_request(
    request: Request,
    _ok: None = Depends(require_endpoint_secret),
):
    """
    Main endpoint for CopilotKit requests.
    Routes requests to the appropriate agent handler based on the agent name.
    """
    try:
        # Staging escape hatch: return a simple success response without invoking agents
        import os
        if os.getenv("CPK_STAGING_FORCE_FALLBACK", "false").lower() == "true":
            body = await request.json()
            thread_id = (
                body.get("threadId")
                if isinstance(body, dict)
                else "fallback-thread"
            )
            return {
                "messages": [
                    {
                        "role": "assistant",
                        "content": [
                            "Connected to backend (staging fallback)."
                        ],
                    }
                ],
                "done": True,
                "threadId": thread_id or "fallback-thread",
            }
        # Parse request body
        body = await request.json()
        logger.info(f"Received CopilotKit request: {str(body)[:200]}...")

        # Extract GraphQL operation if present
        operation_name = body.get("operationName")

        # Handle different request structures
        if operation_name == "generateCopilotResponse":
            # Extract GraphQL variables
            variables = body.get("variables", {})
            data = variables.get("data", {})

            # Get agent name from variables or fall back to default
            agent_name = (
                data.get("agent")
                or data.get("agentName")
                or (data.get("agentSession", {}) or {}).get("agentName")
                or "research_agent"
            )

            if agent_name not in AGENT_HANDLERS:
                logger.warning(f"Unknown agent: {agent_name}")
                return format_graphql_response(
                    {
                        "messages": [
                            {
                                "content": [
                                    f"Agent '{agent_name}' not found. Please specify a valid agent."
                                ],
                                "role": "assistant",
                            }
                        ],
                        "done": True,
                        "threadId": data.get("threadId", "unknown-thread"),
                    }
                )

            # Normalize GraphQL variables to handler-friendly payload
            props = variables.get("properties", {}) or {}
            thread_id = data.get("threadId") or props.get("threadId")
            context = props.get("context")

            # Extract simple messages (role, content) from GraphQL messages format
            simple_messages = []
            for m in data.get("messages", []) or []:
                tm = m.get("textMessage") if isinstance(m, dict) else None
                if isinstance(tm, dict):
                    role = tm.get("role") or "user"
                    content = tm.get("content") or ""
                    simple_messages.append({"role": role, "content": content})

            request_payload = {
                "messages": simple_messages,
                "threadId": thread_id,
                "tenantId": data.get("tenantId") or "default",
                "userId": data.get("userId") or "anonymous",
                "context": context,
            }

            # Process with the appropriate handler
            handler = AGENT_HANDLERS[agent_name]
            result = await handler.handle_request(request_payload)

            # Return formatted GraphQL response
            return format_graphql_response(result)

        else:
            # Direct API request (non-GraphQL)
            agent_name = body.get("agent", "research_agent")

            if agent_name not in AGENT_HANDLERS:
                logger.warning(f"Unknown agent: {agent_name}")
                raise HTTPException(
                    status_code=404, detail=f"Agent '{agent_name}' not found"
                )

            # Process with the appropriate handler
            handler = AGENT_HANDLERS[agent_name]
            try:
                return await handler.handle_request(body)
            except Exception as inner_err:
                logger.exception("Agent handler error for %s: %s", agent_name, inner_err)
                # Return a friendly error message instead of bubbling up
                thread_id = body.get("threadId") if isinstance(body, dict) else "error-thread"
                return {
                    "messages": [
                        {
                            "role": "assistant",
                            "content": [f"Intake handler error: {str(inner_err)}"],
                        }
                    ],
                    "done": True,
                    "threadId": thread_id or "error-thread",
                }

    except Exception as e:
        logger.exception(f"Error handling CopilotKit request: {str(e)}")

        # Return error in GraphQL format if it was a GraphQL request
        if body.get("operationName") == "generateCopilotResponse":
            return format_graphql_response(
                {
                    "messages": [
                        {
                            "content": [
                                "I encountered an error processing your request."
                            ],
                            "role": "assistant",
                        }
                    ],
                    "done": True,
                    "threadId": body.get("variables", {})
                    .get("data", {})
                    .get("threadId", "error-thread"),
                }
            )
        else:
            # Fallback for direct API: return a friendly assistant message instead of 500
            thread_id = body.get("threadId", "fallback-thread") if isinstance(body, dict) else "fallback-thread"
            return {
                "messages": [
                    {
                        "role": "assistant",
                        "content": [
                            "I processed your request (fallback response)."
                        ],
                    }
                ],
                "done": True,
                "threadId": thread_id,
            }


def format_graphql_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Format a result dictionary as a GraphQL response."""
    return {"data": {"generateCopilotResponse": result}}


@router.options("")
async def options_handler():
    """Handle CORS preflight requests."""
    return {}


@router.get("/available-agents")
async def get_available_agents():
    """Return a list of available agents."""
    return {
        "data": {
            "availableAgents": [
                {
                    "id": "research_agent",
                    "name": "Legal Research Assistant",
                    "description": "Specialized agent for legal research in personal injury cases",
                    "capabilities": [
                        "Texas law research",
                        "Personal injury expertise",
                        "Case law citation",
                    ],
                },
                {
                    "id": "deadline_agent",
                    "name": "Legal Deadline Calculator",
                    "description": "Specialized agent for calculating statutory deadlines and filing requirements",
                    "capabilities": [
                        "Statutory deadline calculation",
                        "Multi-jurisdiction support",
                        "Practice area specific deadlines",
                        "MCP Rules Engine integration",
                    ],
                },
            ]
        }
    }
